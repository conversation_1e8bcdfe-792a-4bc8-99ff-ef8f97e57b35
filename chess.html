<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#050A24">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <title>Chess Game</title>
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>♞</text></svg>">
    <!-- jQuery required for chessboard.js -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Load chess.js library for chess logic -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chess.js/0.10.3/chess.min.js"></script>
    <!-- Load chessboard.js and its CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chessboard-js/1.0.0/chessboard-1.0.0.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chessboard-js/1.0.0/chessboard-1.0.0.min.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- PeerJS for P2P multiplayer -->
    <script src="https://unpkg.com/peerjs@1.5.0/dist/peerjs.min.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="chess.css">
    
    <!-- Mobile touch detection script -->
    <script>
        // Detect if device is a touch device
        window.isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        
        // Prevent page scroll when interacting with the board
        document.addEventListener('touchmove', function(e) {
            if (e.target.closest('#game-board') || e.target.closest('#mp-game-board') ||
                e.target.closest('.piece-417db')) {
                e.preventDefault();
            }
        }, {passive: false});
    </script>
    
    <!-- Override styles for chessboard.js drag behavior -->
    <style>
        /* Ensure dragged pieces are always visible */
        img.piece-417db {
            opacity: 1 !important;
            cursor: pointer;
            touch-action: manipulation;
        }

        /* Fix for ghost pieces during drag */
        body > img[src*="chesspieces"] {
            opacity: 1 !important;
            transform: scale(1.15);
            filter: drop-shadow(3px 6px 8px rgba(0, 0, 0, 0.7));
            pointer-events: none;
            z-index: 9999;
        }

        /* Make squares more touch-friendly */
        .square-55d63 {
            cursor: pointer;
            touch-action: manipulation;
        }

        /* Improve piece visibility on mobile */
        @media (max-width: 768px) {
            img.piece-417db {
                transform: scale(1.05);
                transition: transform 0.1s ease;
            }

            img.piece-417db:active {
                transform: scale(1.1);
            }
        }
    </style>
</head>
<body>
    <main class="main-container">
        <h1 class="game-title">Chess</h1>
        
        <!-- P2P Connection -->
        <div id="p2p-lobby" class="p2p-lobby">
            <h2 class="lobby-title">Multiplayer Chess</h2>

            <div class="connection-status">
                <div id="peer-status" class="peer-status">
                    <i class="fas fa-circle status-indicator"></i>
                    <span id="status-text">Initializing...</span>
                </div>
                <div id="peer-id-display" class="peer-id-display">
                    Your ID: <span id="my-peer-id">-</span>
                    <button id="copy-id-btn" class="copy-btn" title="Copy ID">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>

            <div class="lobby-options">
                <div class="option-section">
                    <h3>Create Game</h3>
                    <p>Share your ID with a friend to start playing</p>
                    <button id="create-room-btn" class="action-btn primary">
                        <i class="fas fa-plus"></i>
                        Wait for Player
                    </button>
                </div>

                <div class="option-divider">OR</div>

                <div class="option-section">
                    <h3>Join Game</h3>
                    <p>Enter your friend's ID to join their game</p>
                    <div class="join-form">
                        <input type="text" id="peer-id-input" placeholder="Enter friend's ID" maxlength="50">
                        <button id="join-room-btn" class="action-btn secondary">
                            <i class="fas fa-sign-in-alt"></i>
                            Join Game
                        </button>
                    </div>
                </div>
            </div>

            <div id="connection-progress" class="connection-progress" style="display: none;">
                <div class="loading-spinner"></div>
                <div id="connection-message" class="connection-message">Connecting...</div>
                <button id="cancel-connection-btn" class="action-btn cancel">Cancel</button>
            </div>
        </div>
        

        
        <!-- Game -->
        <div id="game" class="game-mode">
            <div class="game-container">
                <div class="game-board-container">
                    <div id="game-board"></div>
                </div>

                <div class="game-info">
                    <!-- Timer Section -->
                    <div class="timer-container">
                        <div class="timer-section">
                            <div class="timer-label">White</div>
                            <div id="white-timer" class="timer">10:00</div>
                        </div>
                        <div class="timer-section">
                            <div class="timer-label">Black</div>
                            <div id="black-timer" class="timer">10:00</div>
                        </div>
                    </div>

                    <div class="status-container">
                        <div id="status-message" class="status-message">Game ready. White to move.</div>
                        <div id="player-info" class="player-info">
                            <span class="player-info-label">You are playing as:</span>
                            <span id="player-color" class="player-color">White</span>
                        </div>
                        <div class="turn-indicator my-turn">Your Turn</div>
                    </div>

                    <div class="captured-pieces-container">
                        <div class="captured-title">White Captured:</div>
                        <div id="white-captured" class="captured-pieces"></div>
                        <div class="captured-title">Black Captured:</div>
                        <div id="black-captured" class="captured-pieces"></div>
                    </div>

                    <div class="move-list-container">
                        <div class="move-list-title">Move History:</div>
                        <div id="move-list" class="move-list"></div>
                    </div>

                    <div class="footer-credit">
                        <p>Built with ❤️ by <a href="https://www.instagram.com/jangra_gitesh/?igsh=b2hlcW4yeHN1dnhl#" target="_blank">Jangra Gitesh</a></p>
                    </div>


                </div>
            </div>
        </div>
    </main>

    <!-- Game Over Popup -->
    <div id="game-over-popup" class="popup-overlay">
        <div class="popup-content">
            <div class="popup-body">
                <div class="result-icon">
                    <i id="result-icon" class="fas fa-crown"></i>
                </div>
                <h2 id="game-result-title">Game Over</h2>
                <p id="game-result-message">Checkmate! White wins!</p>
                <div class="game-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Moves:</span>
                        <span id="total-moves">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Game Duration:</span>
                        <span id="game-duration">00:00</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Result:</span>
                        <span id="final-result">Checkmate</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Promotion Popup -->
    <div id="promotion-popup" class="popup-overlay">
        <div class="popup-content promotion-content">
            <div class="popup-body">
                <h2>Choose Promotion</h2>
                <p>Select a piece to promote your pawn:</p>
                <div class="promotion-options">
                    <div class="promotion-piece" data-piece="q">
                        <div class="piece-icon"></div>
                        <span>Queen</span>
                    </div>
                    <div class="promotion-piece" data-piece="r">
                        <div class="piece-icon"></div>
                        <span>Rook</span>
                    </div>
                    <div class="promotion-piece" data-piece="b">
                        <div class="piece-icon"></div>
                        <span>Bishop</span>
                    </div>
                    <div class="promotion-piece" data-piece="n">
                        <div class="piece-icon"></div>
                        <span>Knight</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Scripts -->
    <script src="p2p-chess.js"></script>
    <script src="chess.js"></script>
</body>
</html>

